您好：
	如果您是想运行语音识别的一次或若干次的识别过程，本目录下的所有文件适合您直接运行和使用，直接运行一次识别请执行predict_speech_file.py文件，运行语音识别API服务器请使用asrserver_http.py文件，运行基于HTTP协议的语音识别客户端请在使用asrserver_http.py文件运行服务端程序后再使用client_http.py文件，如果需要修改识别的录音文件路径，请在client_http.py中修改。

	如果您想训练自己的语音识别模型，那么GitHub的master分支下的代码可能更适合您，它提供给了更多的选项，以及依赖文件。

	另外，ASRT语音识别项目的发布版本都是一个可稳定使用的软件（前提是依赖环境配置正确），切记不要自作主张直接将本次发布版中训练好的模型拿到GitHub仓库master分支下的代码程序中运行，否则可能由于时间跨度较大，改动较多，导致出现版本不兼容而各种报错的问题，以及不要想当然地随意修改这里的代码，或者使用不符合要求格式的音频数据，如果由于自作主张导致出现的一切问题，如奇奇怪怪的报错，或者几乎无法正确识别的问题，均跟本人无关，跟ASRT语音识别项目无关。

	最后，如果您喜欢本项目，觉得本项目对您能有一些帮助，请在GitHub仓库上点一个Star吧，或者点击进入打赏页面进行打赏。


AI柠檬

--------

GitHub仓库：		https://github.com/nl8590687/ASRT_SpeechRecognition
Gitee镜像仓库：		https://gitee.com/ailemon/ASRT_SpeechRecognition
ASRT项目主页：		https://asrt.ailemon.net
ASRT项目文档：		https://wiki.ailemon.net/docs/asrt-doc
AI柠檬博客：		https://blog.ailemon.net
微信公众号：		AI柠檬博客	(或搜索微信号："ailemon_me")
QQ交流群：		867888133 (1群)	894112051 (2群)

元语音研究网：		https://www.meta-speech.net/		(一个由AI柠檬联合语音业界人士创建的语音技术交流社区)
